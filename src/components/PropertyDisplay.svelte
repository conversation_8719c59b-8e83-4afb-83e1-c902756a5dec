<script lang="ts">
  import StatusBadge from './StatusBadge.svelte';
  import type { SyncStatus } from './types';

  export let label: string;
  export let status: SyncStatus;
  export let value: any;
  export let isTagsList: boolean = false;

  $: displayValue = formatValue(value, isTagsList);

  function formatValue(val: any, isTags: boolean): string {
    if (val === null || val === undefined) {
      return 'None';
    }

    if (typeof val === 'boolean') {
      return val ? 'Yes' : 'No';
    }

    if (isTags && Array.isArray(val)) {
      if (val.length === 0) {
        return 'None';
      }
      return val.map(tag => tag.name || tag).join(', ');
    }

    if (typeof val === 'object' && val.name) {
      return val.name;
    }

    return String(val);
  }

  function getTagsArray(val: any): string[] {
    if (!val || !Array.isArray(val)) {
      return [];
    }
    return val.map(tag => tag.name || tag).filter(Boolean);
  }
</script>

<div class="ghost-sync-property-item">
  <div class="ghost-sync-property-header">
    <span class="ghost-sync-property-label">{label}</span>
    <StatusBadge {status} compact={true} />
  </div>

  <div class="ghost-sync-property-value">
    {#if isTagsList && value && Array.isArray(value) && value.length > 0}
      <div class="ghost-sync-tags-container">
        {#each getTagsArray(value) as tag}
          <span class="ghost-sync-tag-badge">{tag}</span>
        {/each}
      </div>
    {:else}
      <span class="ghost-sync-value-text">{displayValue}</span>
    {/if}
  </div>
</div>

<style>
  .ghost-sync-property-item {
    padding: 12px;
    margin-bottom: 8px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
    transition: all 0.2s ease;
  }

  .ghost-sync-property-item:hover {
    background: var(--background-modifier-hover);
  }

  .ghost-sync-property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }

  .ghost-sync-property-label {
    font-weight: 600;
    font-size: 13px;
    color: var(--text-normal);
    text-transform: capitalize;
  }

  .ghost-sync-property-value {
    margin-left: 4px;
  }

  .ghost-sync-value-text {
    font-size: 12px;
    color: var(--text-muted);
    font-family: var(--font-monospace);
    background: var(--background-primary);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border-hover);
    display: inline-block;
  }

  .ghost-sync-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .ghost-sync-tag-badge {
    display: inline-block;
    padding: 3px 8px;
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
  }

  .ghost-sync-tag-badge:hover {
    background: var(--interactive-accent-hover);
  }
</style>
