<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { TFile } from 'obsidian';
  import PropertyDisplay from './PropertyDisplay.svelte';
  import type { SyncStatusData } from './types';
  import * as path from 'path';

  export let currentFile: TFile | null = null;
  export let syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    synced_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };

  // Helper function to format timestamps according to locale
  function formatTimestamp(timestamp: string | null | undefined): string {
    if (!timestamp || timestamp === 'Never' || timestamp === 'Unknown') {
      return timestamp || 'Never';
    }

    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return timestamp;
      }

      // Format according to user's locale with relative time when recent
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      // Show relative time for recent timestamps
      if (diffMinutes < 1) {
        return 'Just now';
      } else if (diffMinutes < 60) {
        return `${diffMinutes}m ago`;
      } else if (diffHours < 24) {
        return `${diffHours}h ago`;
      } else if (diffDays < 7) {
        return `${diffDays}d ago`;
      } else {
        // For older timestamps, show formatted date
        return date.toLocaleDateString(undefined, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (error) {
      return timestamp;
    }
  }
  export let plugin: any = null;

  const dispatch = createEventDispatcher();

  let isInArticlesDir = false;
  let articlesPath = '';

  $: {
    if (currentFile && plugin?.settings?.articlesDir) {
      articlesPath = path.normalize(plugin.settings.articlesDir);
      const filePath = path.normalize(currentFile.path);
      isInArticlesDir = filePath.startsWith(articlesPath);
    } else {
      isInArticlesDir = false;
    }
  }

  async function handleSmartSync() {
    dispatch('smartSync');
  }

  function handlePublish() {
    dispatch('publish');
  }

  function handleBrowsePosts() {
    if (plugin && plugin.browseGhostPosts) {
      plugin.browseGhostPosts();
    } else {
      dispatch('browsePosts');
    }
  }

  function handleRefresh() {
    dispatch('refresh');
  }
</script>

<div class="ghost-sync-status-view">
  <!-- Header -->
  <div class="ghost-sync-header">
    <h3>Ghost</h3>
    {#if currentFile && !syncStatus.isNewPost}
      <button
        class="ghost-sync-refresh-btn"
        on:click={handleRefresh}
        title="Refresh sync status from Ghost"
      >
        🔄
      </button>
    {/if}
  </div>

  {#if !currentFile}
    <p class="ghost-sync-no-file">No file selected</p>
  {:else if !isInArticlesDir}
    <p class="ghost-sync-not-article">
      File must be in {plugin.settings.articlesDir} directory
    </p>
  {:else if syncStatus.isNewPost}
    <!-- New Post Status -->
    <div class="ghost-sync-new-post">
      <div class="ghost-sync-new-post-icon">📝</div>
      <div class="ghost-sync-new-post-content">
        <h3>New Post</h3>
        <p>This post hasn't been created in Ghost yet.</p>
        <div class="ghost-sync-new-post-details">
          <div><strong>Title:</strong> {syncStatus.localTitle || 'Untitled'}</div>
          <div><strong>Slug:</strong> {syncStatus.localSlug || 'No slug'}</div>
        </div>
        <p class="ghost-sync-new-post-hint">Click "Sync" to create it in Ghost.</p>
      </div>
    </div>

    <!-- Buttons for New Post -->
    <div class="ghost-sync-buttons">
      <button
        class="ghost-sync-btn mod-cta"
        on:click={handleSmartSync}
      >
        Sync to Ghost
      </button>

      <button
        class="ghost-sync-btn"
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>
    </div>
  {:else}

    <!-- Feature Image Preview -->
    {#if syncStatus.ghostPost?.feature_image}
      <div class="ghost-sync-feature-image-container">
        <img
          src={syncStatus.ghostPost.feature_image}
          alt="Featured content"
          class="ghost-sync-feature-image"
        />
      </div>
    {/if}

    <!-- Properties Section - Improved layout -->
    <div class="ghost-sync-properties-section">
      <h4>Properties</h4>
      <div class="ghost-sync-properties-grid">
        <PropertyDisplay
          label="Title"
          status={syncStatus.title}
          value={syncStatus.ghostPost?.title}
        />
        <PropertyDisplay
          label="Slug"
          status={syncStatus.slug}
          value={syncStatus.ghostPost?.slug}
        />
        <PropertyDisplay
          label="Status"
          status={syncStatus.status}
          value={syncStatus.ghostPost?.status}
        />
        <PropertyDisplay
          label="Tags"
          status={syncStatus.tags}
          value={syncStatus.ghostPost?.tags}
          isTagsList={true}
        />
        <PropertyDisplay
          label="Primary Tag"
          status={syncStatus.primary_tag}
          value={syncStatus.ghostPost?.primary_tag?.name}
        />
        <PropertyDisplay
          label="Visibility"
          status={syncStatus.visibility}
          value={syncStatus.ghostPost?.visibility}
        />
        <PropertyDisplay
          label="Featured"
          status={syncStatus.featured}
          value={syncStatus.ghostPost?.featured}
        />
      </div>
    </div>

    <!-- Newsletter Section -->
    {#if syncStatus.ghostPost?.newsletter || syncStatus.ghostPost?.email}
      <div class="ghost-sync-newsletter-section">
        <h4>Newsletter & Email</h4>
        <div class="ghost-sync-newsletter-list">
          <PropertyDisplay
            label="Newsletter"
            status={syncStatus.newsletter}
            value={syncStatus.ghostPost?.newsletter?.name || 'None'}
          />
          <PropertyDisplay
            label="Email Sent"
            status={syncStatus.email_sent}
            value={syncStatus.ghostPost?.email ? 'Yes' : 'No'}
          />
        </div>
      </div>
    {/if}

    <!-- Buttons -->
    <div class="ghost-sync-buttons">
      {#if syncStatus.ghostPost}
        <button
          class="ghost-sync-btn mod-cta"
          class:disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          disabled={syncStatus.ghostPost.status === 'published' && !!syncStatus.ghostPost.email}
          on:click={handlePublish}
        >
          Publish
        </button>
      {/if}

      <button
        class="ghost-sync-btn"
        on:click={handleSmartSync}
      >
        Sync
      </button>

      <button
        class="ghost-sync-btn"
        on:click={handleBrowsePosts}
      >
        Browse Posts
      </button>
    </div>

    <!-- Sync Metadata Section - Moved to bottom and made subtle -->
    <div class="ghost-sync-metadata-subtle">
      <div class="ghost-sync-metadata-item">
        <span class="label">Last Synced:</span>
        <span class="value">{formatTimestamp(syncStatus.localSyncedAt)}</span>
      </div>
      <div class="ghost-sync-metadata-item">
        <span class="label">Last Changed:</span>
        <span class="value">{formatTimestamp(syncStatus.localChangedAt)}</span>
      </div>
    </div>

  {/if}
</div>

<style>
  .ghost-sync-feature-image-container {
    margin-bottom: 16px;
  }

  .ghost-sync-feature-image {
    width: 100%;
    max-height: 200px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .ghost-sync-btn.disabled:hover {
    opacity: 0.5;
  }

  .ghost-sync-metadata {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-metadata h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-sync-properties-section {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-properties-section h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-sync-newsletter-section {
    margin-top: 16px;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
  }

  .ghost-sync-newsletter-section h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .ghost-sync-newsletter-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ghost-sync-metadata-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
  }

  .ghost-sync-metadata-item .label {
    color: var(--text-muted);
    font-weight: 500;
  }

  .ghost-sync-metadata-item .value {
    color: var(--text-normal);
    font-family: var(--font-monospace);
  }

  /* Subtle metadata section at bottom */
  .ghost-sync-metadata-subtle {
    margin-top: 20px;
    padding: 8px 12px;
    background: transparent;
    border-top: 1px solid var(--background-modifier-border-hover);
    opacity: 0.7;
  }

  .ghost-sync-metadata-subtle .ghost-sync-metadata-item {
    padding: 2px 0;
    font-size: 11px;
  }

  .ghost-sync-metadata-subtle .label {
    color: var(--text-faint);
    font-weight: normal;
  }

  .ghost-sync-metadata-subtle .value {
    color: var(--text-muted);
    font-family: var(--font-interface);
    font-size: 11px;
  }

  /* Refresh button in header */
  .ghost-sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .ghost-sync-refresh-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .ghost-sync-refresh-btn:hover {
    color: var(--text-normal);
    background: var(--background-modifier-hover);
  }

  .ghost-sync-refresh-btn:active {
    transform: rotate(180deg);
  }

  /* Improved properties section */
  .ghost-sync-properties-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-normal);
    font-size: 14px;
    font-weight: 600;
  }

  .ghost-sync-properties-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  /* Newsletter section styling */
  .ghost-sync-newsletter-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-normal);
    font-size: 14px;
    font-weight: 600;
  }

  .ghost-sync-newsletter-list {
    display: flex;
    flex-direction: column;
    gap: 0;
  }
</style>
